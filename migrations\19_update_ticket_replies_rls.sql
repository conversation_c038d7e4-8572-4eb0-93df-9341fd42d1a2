-- Migration to add tenant_id to tickets table and update RLS policies
-- This migration combines ticket tenant association and RLS policy updates

-- Step 1: Add tenant_id to tickets table
ALTER TABLE tickets ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id) ON DELETE SET NULL;
CREATE INDEX IF NOT EXISTS idx_tickets_tenant_id ON tickets(tenant_id);

-- Step 2: Update views for ticket details
DROP VIEW IF EXISTS ticket_details CASCADE;
CREATE VIEW ticket_details WITH (security_invoker=true) AS
SELECT 
    t.id,
    t.ticket_number,
    t.title,
    t.description,
    t.category,
    t.priority,
    t.status,
    t.created_at,
    t.updated_at,
    t.created_by,
    t.assigned_to,
    t.tenant_id,
    p1.display_name as created_by_name,
    p1.first_name as created_by_first_name,
    p1.last_name as created_by_last_name,
    p1.role as created_by_role,
    p2.display_name as assigned_to_name,
    p2.first_name as assigned_to_first_name,
    p2.last_name as assigned_to_last_name,
    p2.role as assigned_to_role,
    tn.first_name as tenant_first_name,
    tn.last_name as tenant_last_name,
    tn.email as tenant_email
FROM 
    tickets t
LEFT JOIN
    public.user_profiles p1 ON t.created_by = p1.id
LEFT JOIN
    public.user_profiles p2 ON t.assigned_to = p2.id
LEFT JOIN
    public.tenants tn ON t.tenant_id = tn.id;

-- Step 3: Recreate dependent views
DROP VIEW IF EXISTS admin_tickets;
CREATE VIEW admin_tickets WITH (security_invoker=true) AS
SELECT *
FROM ticket_details
WHERE has_valid_role() AND can_manage_all_tickets();

DROP VIEW IF EXISTS tenant_tickets;
CREATE VIEW tenant_tickets WITH (security_invoker=true) AS
SELECT *
FROM ticket_details
WHERE has_valid_role() AND (
    created_by = (select auth.uid()) 
    OR assigned_to = (select auth.uid())
    OR EXISTS (
        SELECT 1 FROM tenants t 
        WHERE t.id = ticket_details.tenant_id 
        AND t.user_id = (select auth.uid())
    )
);

-- Step 4: Update tickets RLS policy
DROP POLICY IF EXISTS tickets_select_policy ON tickets;
CREATE POLICY tickets_select_policy ON tickets 
    FOR SELECT 
    USING (
        has_valid_role() AND (
            (select auth.uid()) = created_by 
            OR (select auth.uid()) = assigned_to
            OR can_manage_all_tickets()
            OR EXISTS (
                SELECT 1 FROM tenants t 
                WHERE t.id = tickets.tenant_id 
                AND t.user_id = (select auth.uid())
            )
        )
    );

-- Step 5: Create function to ensure user profile exists
CREATE OR REPLACE FUNCTION ensure_user_profile(
    user_id UUID,
    user_email TEXT DEFAULT NULL,
    user_metadata JSONB DEFAULT NULL
) RETURNS void AS $$
BEGIN
    -- Insert into user_profiles if not exists
    INSERT INTO user_profiles (id, role, display_name)
    VALUES (
        user_id,
        'tenant'::user_role,
        COALESCE(
            user_metadata->>'full_name',
            'User ' || substring(user_id::text, 1, 8)
        )
    )
    ON CONFLICT (id) DO NOTHING;

    -- Update display name from metadata if available
    IF user_metadata IS NOT NULL AND user_metadata->>'full_name' IS NOT NULL THEN
        UPDATE user_profiles
        SET 
            display_name = user_metadata->>'full_name',
            first_name = SPLIT_PART(user_metadata->>'full_name', ' ', 1),
            last_name = CASE 
                WHEN array_length(string_to_array(user_metadata->>'full_name', ' '), 1) > 1 
                THEN array_to_string((string_to_array(user_metadata->>'full_name', ' '))[2:], ' ')
                ELSE NULL 
            END
        WHERE id = user_id
        AND (display_name IS NULL OR display_name = '');
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Ensure existing users have profiles
DO $$ 
BEGIN
    -- Create profiles for existing users
    INSERT INTO user_profiles (id, role, display_name)
    SELECT 
        au.id,
        'tenant'::user_role,
        COALESCE(
            au.raw_user_meta_data->>'full_name',
            'User ' || substring(au.id::text, 1, 8)
        )
    FROM auth.users au
    WHERE NOT EXISTS (
        SELECT 1 FROM user_profiles up WHERE up.id = au.id
    );

    -- Update display names from auth.users metadata
    UPDATE user_profiles up
    SET 
        display_name = COALESCE(au.raw_user_meta_data->>'full_name', up.display_name),
        first_name = SPLIT_PART(COALESCE(au.raw_user_meta_data->>'full_name', up.display_name), ' ', 1),
        last_name = CASE 
            WHEN array_length(string_to_array(COALESCE(au.raw_user_meta_data->>'full_name', up.display_name), ' '), 1) > 1 
            THEN array_to_string((string_to_array(COALESCE(au.raw_user_meta_data->>'full_name', up.display_name), ' '))[2:], ' ')
            ELSE NULL 
        END
    FROM auth.users au
    WHERE up.id = au.id
    AND (up.display_name IS NULL OR up.display_name = '');
END $$;

-- Step 7: Update ticket_replies RLS policies
DROP POLICY IF EXISTS ticket_replies_insert_policy ON ticket_replies;
DROP POLICY IF EXISTS ticket_replies_select_policy ON ticket_replies;
DROP POLICY IF EXISTS ticket_replies_update_policy ON ticket_replies;
DROP POLICY IF EXISTS ticket_replies_delete_policy ON ticket_replies;

-- RLS Policy: Users can view replies for tickets they have access to
CREATE POLICY ticket_replies_select_policy ON ticket_replies 
    FOR SELECT 
    USING (
        has_valid_role() AND (
            EXISTS (
                SELECT 1 FROM tickets t
                WHERE t.id = ticket_replies.ticket_id
                AND (
                    (select auth.uid()) = t.created_by 
                    OR (select auth.uid()) = t.assigned_to
                    OR can_manage_all_tickets()
                    OR EXISTS (
                        SELECT 1 FROM tenants tn 
                        WHERE tn.id = t.tenant_id 
                        AND tn.user_id = (select auth.uid())
                    )
                )
            )
            AND (
                -- Hide internal replies from non-admin users
                NOT is_internal OR can_manage_all_tickets()
            )
        )
    );

-- RLS Policy: Users can insert replies for tickets they have access to
CREATE POLICY ticket_replies_insert_policy ON ticket_replies 
    FOR INSERT 
    WITH CHECK (
        has_valid_role() AND
        (select auth.uid()) = user_id
        AND EXISTS (
            SELECT 1 FROM tickets t
            WHERE t.id = ticket_replies.ticket_id
            AND (
                (select auth.uid()) = t.created_by 
                OR (select auth.uid()) = t.assigned_to
                OR can_manage_all_tickets()
                OR EXISTS (
                    SELECT 1 FROM tenants tn 
                    WHERE tn.id = t.tenant_id 
                    AND tn.user_id = (select auth.uid())
                )
            )
        )
        AND (
            -- Non-admin users cannot create internal replies
            NOT is_internal OR can_manage_all_tickets()
        )
        -- Ensure reply_type is valid
        AND reply_type IN ('comment', 'status_update', 'assignment')
    );

-- RLS Policy: Users can update their own replies
CREATE POLICY ticket_replies_update_policy ON ticket_replies 
    FOR UPDATE 
    USING (
        has_valid_role() AND (
            (select auth.uid()) = user_id
            OR can_manage_all_tickets()
        )
    );

-- RLS Policy: Only managers can delete replies
CREATE POLICY ticket_replies_delete_policy ON ticket_replies
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles
            WHERE id = (select auth.uid()) AND role = 'manager'
        )
    );

-- Add helpful comments
COMMENT ON COLUMN tickets.tenant_id IS 'Reference to the tenant this ticket is associated with. This allows tickets to be directly linked to specific tenants.';

COMMENT ON FUNCTION ensure_user_profile(UUID, TEXT, JSONB) IS 'Creates or updates a user_profile record for a given user ID. Can be called manually when needed.';

COMMENT ON POLICY ticket_replies_select_policy ON ticket_replies IS 
'Controls who can view ticket replies. Users can see replies for tickets they have access to, but non-admin users cannot see internal replies.';

COMMENT ON POLICY ticket_replies_insert_policy ON ticket_replies IS 
'Controls who can add replies. Users can add replies to tickets they have access to, but only admins/managers can create internal replies.';

COMMENT ON POLICY ticket_replies_update_policy ON ticket_replies IS 
'Controls who can update replies. Users can update their own replies, while admins can update any reply.';

COMMENT ON POLICY ticket_replies_delete_policy ON ticket_replies IS 
'Controls who can delete replies. Only managers can delete replies.'; 