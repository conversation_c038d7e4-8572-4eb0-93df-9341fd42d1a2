import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/ticket.dart';
import '../models/ticket_reply.dart';

class TicketService {
  final SupabaseClient _supabaseClient = Supabase.instance.client;

  // Get current user's role
  Future<UserRole> getCurrentUserRole() async {
    try {
      final userId = _supabaseClient.auth.currentUser!.id;
      final response = await _supabaseClient
          .from('user_profiles')
          .select('role')
          .eq('id', userId)
          .single();
      
      final roleString = response['role'] as String;
      return UserRole.values.firstWhere(
        (role) => role.name == roleString,
        orElse: () => UserRole.tenant,
      );
    } catch (e) {
      debugPrint('Error fetching user role: $e');
      return UserRole.tenant; // Default to tenant if error
    }
  }

  // Get all tickets based on user role
  Future<List<Ticket>> getUserTickets() async {
    try {
      final userRole = await getCurrentUserRole();
      
      // Use different views based on role
      final String viewName = 
          (userRole == UserRole.admin || userRole == UserRole.manager) 
          ? 'admin_tickets' 
          : 'tenant_tickets';
      
      // Get all tickets
      final ticketsResponse = await _supabaseClient
          .from(viewName)
          .select()
          .order('created_at', ascending: false);
      
      // Convert to Ticket objects
      final tickets = (ticketsResponse as List<dynamic>)
          .map((ticket) => Ticket.fromJson(ticket))
          .toList();
      
      // Get reply counts for each ticket
      final ticketIds = tickets.map((t) => t.id).toList();
      if (ticketIds.isEmpty) return tickets;
      
      // Get tickets that have replies
      final repliesResponse = await _supabaseClient
          .from('ticket_replies')
          .select('ticket_id')
          .inFilter('ticket_id', ticketIds);
          
      // Create a map of ticket IDs to whether they have replies
      final Map<String, bool> ticketHasReplies = {};
      for (final reply in repliesResponse as List<dynamic>) {
        ticketHasReplies[reply['ticket_id'] as String] = true;
      }
      
      // Update tickets with reply information
      return tickets.map((ticket) {
        return Ticket(
          id: ticket.id,
          ticketNumber: ticket.ticketNumber,
          title: ticket.title,
          description: ticket.description,
          category: ticket.category,
          priority: ticket.priority,
          status: ticket.status,
          createdBy: ticket.createdBy,
          assignedTo: ticket.assignedTo,
          createdAt: ticket.createdAt,
          updatedAt: ticket.updatedAt,
          createdByName: ticket.createdByName,
          createdByFirstName: ticket.createdByFirstName,
          createdByLastName: ticket.createdByLastName,
          createdByRole: ticket.createdByRole,
          assignedToName: ticket.assignedToName,
          assignedToFirstName: ticket.assignedToFirstName,
          assignedToLastName: ticket.assignedToLastName,
          assignedToRole: ticket.assignedToRole,
          hasReplies: ticketHasReplies[ticket.id] ?? false,
        );
      }).toList();
    } catch (e) {
      debugPrint('Error fetching tickets: $e');
      rethrow;
    }
  }

  // Get ticket by ID
  Future<Ticket> getTicketById(String ticketId) async {
    try {
      final response = await _supabaseClient
          .from('ticket_details')
          .select()
          .eq('id', ticketId)
          .single();

      return Ticket.fromJson(response);
    } catch (e) {
      debugPrint('Error fetching ticket: $e');
      rethrow;
    }
  }

  // Create a new ticket
  Future<Ticket> createTicket({
    required String title,
    required String description,
    required TicketCategory category,
    required TicketPriority priority,
  }) async {
    try {
      final userId = _supabaseClient.auth.currentUser!.id;

      final ticketData = {
        'title': title,
        'description': description,
        'category': category.name,
        'priority': priority.name,
        'created_by': userId,
        'status': TicketStatus.inProgress.name, // Always set status to inProgress
      };

      final response = await _supabaseClient
          .from('tickets')
          .insert(ticketData)
          .select()
          .single();

      return getTicketById(response['id']);
    } catch (e) {
      debugPrint('Error creating ticket: $e');
      rethrow;
    }
  }

  // Update ticket status
  Future<void> updateTicketStatus(String ticketId, TicketStatus status) async {
    try {
      await _supabaseClient
          .from('tickets')
          .update({
            'status': status.name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', ticketId);
    } catch (e) {
      debugPrint('Error updating ticket status: $e');
      rethrow;
    }
  }

  // Assign ticket to user
  Future<void> assignTicket(String ticketId, String userId) async {
    try {
      await _supabaseClient
          .from('tickets')
          .update({
            'assigned_to': userId,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', ticketId);
    } catch (e) {
      debugPrint('Error assigning ticket: $e');
      rethrow;
    }
  }

  // Get tickets by status
  Future<List<Ticket>> getTicketsByStatus(TicketStatus status) async {
    try {
      final userRole = await getCurrentUserRole();
      
      // Use different views based on role
      final String viewName = 
          (userRole == UserRole.admin || userRole == UserRole.manager) 
          ? 'admin_tickets' 
          : 'tenant_tickets';
      
      final response = await _supabaseClient
          .from(viewName)
          .select()
          .eq('status', status.name)
          .order('created_at', ascending: false);

      return (response as List<dynamic>)
          .map((ticket) => Ticket.fromJson(ticket))
          .toList();
    } catch (e) {
      debugPrint('Error fetching tickets by status: $e');
      rethrow;
    }
  }
  
  // Delete ticket (only for managers)
  Future<void> deleteTicket(String ticketId) async {
    try {
      final userRole = await getCurrentUserRole();
      
      if (userRole != UserRole.manager) {
        throw Exception('Only managers can delete tickets');
      }
      
      await _supabaseClient
          .from('tickets')
          .delete()
          .eq('id', ticketId);
    } catch (e) {
      debugPrint('Error deleting ticket: $e');
      rethrow;
    }
  }
  
  // Get ticket replies
  Future<List<TicketReply>> getTicketReplies(String ticketId) async {
    try {
      final userRole = await getCurrentUserRole();
      
      // Use different views based on role
      final String viewName = 
          (userRole == UserRole.admin || userRole == UserRole.manager) 
          ? 'admin_ticket_replies' 
          : 'tenant_ticket_replies';
      
      final response = await _supabaseClient
          .from(viewName)
          .select()
          .eq('ticket_id', ticketId)
          .order('created_at', ascending: true);

      return (response as List<dynamic>)
          .map((reply) => TicketReply.fromJson(reply))
          .toList();
    } catch (e) {
      debugPrint('Error fetching ticket replies: $e');
      rethrow;
    }
  }
  
  // Add a reply to a ticket
  Future<TicketReply> addTicketReply({
    required String ticketId,
    required String message,
    bool isInternal = false,
  }) async {
    try {
      final userId = _supabaseClient.auth.currentUser!.id;

      final replyData = {
        'ticket_id': ticketId,
        'user_id': userId,
        'message': message,
        'reply_type': 'comment',
        'is_internal': isInternal,
      };

      final response = await _supabaseClient
          .from('ticket_replies')
          .insert(replyData)
          .select('id')
          .single();

      // Get the complete reply with user information
      final userRole = await getCurrentUserRole();
      final String viewName = 
          (userRole == UserRole.admin || userRole == UserRole.manager) 
          ? 'admin_ticket_replies' 
          : 'tenant_ticket_replies';
          
      final replyResponse = await _supabaseClient
          .from(viewName)
          .select()
          .eq('id', response['id'])
          .single();

      return TicketReply.fromJson(replyResponse);
    } catch (e) {
      debugPrint('Error adding ticket reply: $e');
      rethrow;
    }
  }
  
  // Update a ticket reply
  Future<void> updateTicketReply(String replyId, String message) async {
    try {
      await _supabaseClient
          .from('ticket_replies')
          .update({
            'message': message,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', replyId);
    } catch (e) {
      debugPrint('Error updating ticket reply: $e');
      rethrow;
    }
  }
  
  // Delete a ticket reply (only for managers)
  Future<void> deleteTicketReply(String replyId) async {
    try {
      final userRole = await getCurrentUserRole();
      
      if (userRole != UserRole.manager) {
        throw Exception('Only managers can delete replies');
      }
      
      await _supabaseClient
          .from('ticket_replies')
          .delete()
          .eq('id', replyId);
    } catch (e) {
      debugPrint('Error deleting ticket reply: $e');
      rethrow;
    }
  }
  
  // Get reply count for a ticket
  Future<int> getTicketReplyCount(String ticketId) async {
    try {
      final response = await _supabaseClient
          .rpc('get_ticket_reply_count', params: {'ticket_uuid': ticketId});
      return response ?? 0;
    } catch (e) {
      debugPrint('Error getting ticket reply count: $e');
      return 0;
    }
  }
}
