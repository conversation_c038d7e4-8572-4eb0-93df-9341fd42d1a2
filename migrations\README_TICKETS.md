# Ticket System Security Fixes

This document outlines the security fixes implemented in the ticket system to address various security vulnerabilities.

## Security Issues Fixed

### 1. Security Definer Views

**Issue:** The original implementation contained views that were implicitly using `SECURITY DEFINER`, which is a potential security risk. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user.

**Affected Views:**
- `public.ticket_details`
- `public.ticket_replies_details`
- `public.admin_tickets`
- `public.tenant_tickets`
- `public.tenant_ticket_replies`
- `public.admin_ticket_replies`

**Fix:** All views have been recreated with explicit `SECURITY INVOKER` setting using the `WITH (security_invoker=true)` clause.

### 2. Function Search Path Mutable

**Issue:** Functions were defined without setting the search_path parameter, making them vulnerable to schema injection attacks.

**Affected Functions:**
- `public.get_user_display_name`
- `public.can_manage_all_tickets`
- `public.update_ticket_on_reply`
- `public.create_status_change_reply`
- `public.get_ticket_reply_count`
- `public.get_latest_ticket_reply`
- `public.set_ticket_number`
- `public.log_ticket_creation`

**Fix:** All functions now include `SET search_path = public` to prevent schema injection attacks.

### 3. Tenant Ticket Creation Error

**Issue:** Tenants were unable to create tickets due to RLS policy violations, resulting in the error: "new row violates row-level security policy for table tickets".

**Fix:**
1. Ensured all authenticated users have a corresponding entry in the `user_profiles` table
2. Added a role column to the `user_profiles` table with a default value of 'tenant'
3. Improved the `tickets_insert_policy` to properly handle tenant ticket creation
4. Added logging for ticket creation attempts to help with debugging

### 4. Role-Based Access Control

**Issue:** Access control was inconsistent across different operations and didn't properly check for valid user roles.

**Fix:**
1. Added a new `has_valid_role()` function that checks if a user has one of the valid roles (tenant, admin, or manager)
2. Updated all RLS policies to first check if the user has a valid role before granting access
3. Modified all views to include role validation in their WHERE clauses
4. Ensured consistent access patterns across all ticket-related operations

## Security Improvements

### View Security

- All views now use `WITH (security_invoker=true)` to ensure they respect RLS policies
- Views no longer expose sensitive auth data by using only `public.user_profiles` instead of `auth.users`
- All views now check for valid user roles before returning data

### Function Security

- All functions use `SECURITY INVOKER` to ensure they run with the permissions of the calling user
- All functions have `search_path` explicitly set to `public` to prevent schema injection attacks

### RLS Policies

- Clear separation of access permissions for tenants, admins, and managers
- Proper checks for ticket ownership and assignment
- Protection of internal replies from unauthorized access
- Consistent role validation across all policies

### User Profiles

- Enhanced user profile data with display names, first names, and last names
- Automatic creation of user profile entries for all authenticated users
- Default display names for users without explicit profile information
- Role-based access control using the role column in user_profiles

## Testing Recommendations

After applying these security fixes, verify that:

1. Tenants can successfully create new tickets
2. Tenants can only see their own tickets and non-internal replies
3. Admins and managers can see all tickets and replies as appropriate
4. RLS policies are correctly enforced for all user roles
5. No unauthorized data access is possible through the views
6. Functions correctly resolve database objects from the public schema
7. Users without valid roles cannot access any ticket-related functionality

## Additional Security Recommendations

### Auth Security Enhancements

In addition to the database-level fixes, the following Auth security enhancements are recommended:

1. **Enable Leaked Password Protection**
   - Prevents the use of compromised passwords by checking against HaveIBeenPwned.org
   - Remediation: https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection

2. **Enable More MFA Options**
   - Strengthens account security by requiring multiple authentication factors
   - Remediation: https://supabase.com/docs/guides/auth/auth-mfa 