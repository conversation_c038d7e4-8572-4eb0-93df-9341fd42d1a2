import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/ticket.dart';
import '../models/ticket_reply.dart';
import '../services/ticket_service.dart';

class TicketDetailScreen extends StatefulWidget {
  final String ticketId;

  const TicketDetailScreen({super.key, required this.ticketId});

  @override
  State<TicketDetailScreen> createState() => _TicketDetailScreenState();
}

class _TicketDetailScreenState extends State<TicketDetailScreen> {
  final TicketService _ticketService = TicketService();
  bool _isLoading = true;
  String _errorMessage = '';
  Ticket? _ticket;
  bool _isUpdating = false;
  UserRole? _currentUserRole;
  String? _currentUserId;
  List<TicketReply> _replies = [];
  bool _isLoadingReplies = false;
  final TextEditingController _replyController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadTicket();
    _loadUserRole();
  }
  
  @override
  void dispose() {
    _replyController.dispose();
    super.dispose();
  }

  Future<void> _loadUserRole() async {
    try {
      final userRole = await _ticketService.getCurrentUserRole();
      final userId = Supabase.instance.client.auth.currentUser!.id;
      setState(() {
        _currentUserRole = userRole;
        _currentUserId = userId;
      });
    } catch (e) {
      debugPrint('Error loading user role: $e');
    }
  }

  Future<void> _loadTicket() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final ticket = await _ticketService.getTicketById(widget.ticketId);
      
      // Automatically update any 'open' tickets to 'inProgress'
      if (ticket.status == TicketStatus.open) {
        await _ticketService.updateTicketStatus(ticket.id, TicketStatus.inProgress);
        // Reload the ticket to get updated status
        final updatedTicket = await _ticketService.getTicketById(widget.ticketId);
        setState(() {
          _ticket = updatedTicket;
          _isLoading = false;
        });
      } else {
        setState(() {
          _ticket = ticket;
          _isLoading = false;
        });
      }
      
      // Load replies after ticket is loaded
      _loadReplies();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load ticket: ${e.toString()}';
        _isLoading = false;
      });
    }
  }
  
  Future<void> _loadReplies() async {
    if (_ticket == null) return;
    
    setState(() {
      _isLoadingReplies = true;
    });

    try {
      final replies = await _ticketService.getTicketReplies(_ticket!.id);
      setState(() {
        _replies = replies;
        _isLoadingReplies = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingReplies = false;
      });
      debugPrint('Error loading replies: $e');
    }
  }
  
  Future<void> _addReply() async {
    if (_replyController.text.trim().isEmpty) return;
    
    try {
      setState(() {
        _isUpdating = true;
      });
      
      await _ticketService.addTicketReply(
        ticketId: _ticket!.id,
        message: _replyController.text.trim(),
      );
      
      _replyController.clear();
      await _loadReplies();
      
      setState(() {
        _isUpdating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Reply added successfully')),
        );
      }
    } catch (e) {
      setState(() {
        _isUpdating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to add reply: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _updateTicketStatus(TicketStatus newStatus) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await _ticketService.updateTicketStatus(widget.ticketId, newStatus);
      // Reload both ticket and replies to get latest data including system messages
      await _loadTicket();
      await _loadReplies();
      
      setState(() {
        _isUpdating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ticket status updated to ${_getStatusDisplayText(newStatus)}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to update ticket status: ${e.toString()}';
        _isUpdating = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteTicket() async {
    if (_currentUserRole != UserRole.manager) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Only managers can delete tickets')),
      );
      return;
    }

    setState(() {
      _isUpdating = true;
    });

    try {
      await _ticketService.deleteTicket(widget.ticketId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Ticket deleted successfully')),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to delete ticket: ${e.toString()}';
        _isUpdating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: _ticket != null
            ? Text('Ticket - ${_ticket!.ticketNumber}')
            : const Text('Ticket Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTicket,
          ),
          if (_currentUserRole == UserRole.manager && _ticket != null)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteConfirmation(),
              tooltip: 'Delete Ticket',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(child: Text(_errorMessage))
              : _ticket == null
                  ? const Center(child: Text('Ticket not found'))
                  : _buildTicketDetails(),
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Ticket'),
        content: const Text('Are you sure you want to delete this ticket? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteTicket();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildTicketDetails() {
    return Stack(
      children: [
        SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card with Title and Status
              _buildHeaderCard(),
              const SizedBox(height: 16),
              
              // Status Update Dropdown (at the top)
              if (_ticket!.status != TicketStatus.closed && 
                  (_currentUserId == _ticket!.createdBy || 
                   _currentUserId == _ticket!.assignedTo || 
                   _currentUserRole == UserRole.admin || 
                   _currentUserRole == UserRole.manager))
                _buildStatusDropdown(),
              
              if (_ticket!.status != TicketStatus.closed && 
                  (_currentUserId == _ticket!.createdBy || 
                   _currentUserId == _ticket!.assignedTo || 
                   _currentUserRole == UserRole.admin || 
                   _currentUserRole == UserRole.manager))
                const SizedBox(height: 16),
              
              // Reply Input (at the top)
              if (_ticket!.status != TicketStatus.closed && 
                  (_currentUserId == _ticket!.createdBy || 
                   _currentUserId == _ticket!.assignedTo || 
                   _currentUserRole == UserRole.admin || 
                   _currentUserRole == UserRole.manager))
                _buildReplyInput(),
              
              if (_ticket!.status != TicketStatus.closed && 
                  (_currentUserId == _ticket!.createdBy || 
                   _currentUserId == _ticket!.assignedTo || 
                   _currentUserRole == UserRole.admin || 
                   _currentUserRole == UserRole.manager))
                const SizedBox(height: 16),
              
              // Replies Section
              _buildRepliesSection(),
              const SizedBox(height: 16),
              
              // Description Card
              _buildDescriptionCard(),
              const SizedBox(height: 16),
              
              // Details Card
              _buildDetailsCard(),
              const SizedBox(height: 100), // Extra padding for better scrolling
            ],
          ),
        ),
        if (_isUpdating)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: const Center(child: CircularProgressIndicator()),
          ),
      ],
    );
  }


  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildStatusChip(Ticket ticket) {
    return Chip(
      label: Text(
        ticket.getStatusDisplay(),
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: ticket.getStatusColor(),
      padding: EdgeInsets.zero,
      labelPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      visualDensity: VisualDensity.compact,
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      elevation: 3,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.05),
              Colors.white,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.support_agent,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _ticket!.title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _ticket!.getFormattedTicketNumber(),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildStatusChip(_ticket!),
                _buildPriorityChip(_ticket!),
                _buildCategoryChip(_ticket!),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue[50]!,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.description_outlined,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Description',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                _ticket!.description.isNotEmpty 
                    ? _ticket!.description 
                    : 'No description provided',
                style: TextStyle(
                  fontSize: 15,
                  height: 1.6,
                  color: _ticket!.description.isNotEmpty 
                      ? Colors.black87 
                      : Colors.grey[500],
                  fontStyle: _ticket!.description.isNotEmpty 
                      ? FontStyle.normal 
                      : FontStyle.italic,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 14,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 6),
                Text(
                  'Ticket ID: ${_ticket!.getFormattedTicketNumber()}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.grey[50]!,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Ticket Details',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  _buildInfoRow('Created by', _ticket!.getCreatedByFirstName()),
                  const Divider(height: 16),
                  _buildInfoRow('Created on', _formatDate(_ticket!.createdAt)),
                  if (_ticket!.assignedTo != null) ...[
                    const Divider(height: 16),
                    _buildInfoRow('Assigned to', _ticket!.getAssignedToDisplayName()),
                  ],
                  const Divider(height: 16),
                  _buildInfoRow('Last updated', _formatDate(_ticket!.updatedAt)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusDropdown() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.green[50]!,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.edit_outlined,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Update Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: DropdownButtonFormField<TicketStatus>(
                value: _ticket!.status,
                icon: const Icon(Icons.arrow_drop_down),
                iconSize: 24,
                elevation: 16,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.0),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.transparent,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onChanged: (TicketStatus? newValue) async {
                  if (newValue != null && _ticket!.status != newValue) {
                    await _updateTicketStatus(newValue);
                  }
                },
                items: _getAvailableStatusOptions().map<DropdownMenuItem<TicketStatus>>((TicketStatus status) {
                  String displayText;
                  switch (status) {
                    case TicketStatus.inProgress:
                      displayText = 'In Progress';
                      break;
                    default:
                      displayText = status.name[0].toUpperCase() + status.name.substring(1);
                  }
                  return DropdownMenuItem<TicketStatus>(
                    value: status,
                    child: Text(
                      displayText,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityChip(Ticket ticket) {
    return Chip(
      label: Text(
        ticket.priority.name[0].toUpperCase() + ticket.priority.name.substring(1),
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: ticket.getPriorityColor(),
      padding: EdgeInsets.zero,
      labelPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      visualDensity: VisualDensity.compact,
    );
  }

  Widget _buildCategoryChip(Ticket ticket) {
    return Chip(
      label: Text(
        ticket.category.name[0].toUpperCase() + ticket.category.name.substring(1),
        style: const TextStyle(fontSize: 12),
      ),
      backgroundColor: Colors.grey[200],
      padding: EdgeInsets.zero,
      labelPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      visualDensity: VisualDensity.compact,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
  
  List<TicketStatus> _getAvailableStatusOptions() {
    // Define status progression logic
    switch (_ticket!.status) {
      case TicketStatus.open:
        // From open, can go to in progress or closed (skip resolution for immediate closure)
        return [TicketStatus.inProgress, TicketStatus.closed];
      case TicketStatus.inProgress:
        // From in progress, can go to resolved or closed (no option to go back to open)
        return [TicketStatus.inProgress, TicketStatus.resolved, TicketStatus.closed];
      case TicketStatus.resolved:
        // From resolved, can go to closed or back to in progress if issue resurfaces
        return [TicketStatus.inProgress, TicketStatus.resolved, TicketStatus.closed];
      case TicketStatus.closed:
        // From closed, can only reopen to in progress
        return [TicketStatus.inProgress, TicketStatus.closed];
    }
  }
  
  String _getStatusDisplayText(TicketStatus status) {
    switch (status) {
      case TicketStatus.inProgress:
        return 'In Progress';
      default:
        return status.name[0].toUpperCase() + status.name.substring(1);
    }
  }
  
  Widget _buildRepliesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.chat_bubble_outline),
                const SizedBox(width: 8),
                Text(
                  'Replies (${_replies.length})',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoadingReplies)
              const Center(child: CircularProgressIndicator())
            else if (_replies.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text(
                    'No replies yet. Be the first to add a comment!',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _replies.length,
                separatorBuilder: (context, index) => const SizedBox(height: 12),
                itemBuilder: (context, index) {
                  final reply = _replies[index];
                  return _buildReplyItem(reply);
                },
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildReplyItem(TicketReply reply) {
    final isCurrentUser = _currentUserId == reply.userId;
    final isSystemReply = reply.isSystemReply();
    
    return Container(
      margin: EdgeInsets.only(
        left: isCurrentUser ? 40.0 : 0.0,
        right: isCurrentUser ? 0.0 : 40.0,
        bottom: 12.0,
      ),
      child: Card(
        elevation: isCurrentUser ? 3 : 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: isCurrentUser 
            ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
            : isSystemReply 
                ? Colors.amber[50] 
                : Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 12,
                    backgroundColor: reply.getReplyTypeColor(),
                    child: Icon(
                      reply.getReplyTypeIcon(),
                      size: 14,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    reply.getDisplayName(),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (reply.userRole != null)
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: reply.userRole == 'admin' || reply.userRole == 'manager'
                            ? Colors.purple[100]
                            : Colors.blue[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        reply.userRole!.toUpperCase(),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: reply.userRole == 'admin' || reply.userRole == 'manager'
                              ? Colors.purple[800]
                              : Colors.blue[800],
                        ),
                      ),
                    ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      reply.getFormattedTime(),
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSystemReply 
                      ? Colors.amber[25] 
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  reply.message,
                  style: TextStyle(
                    fontSize: 14,
                    fontStyle: isSystemReply ? FontStyle.italic : FontStyle.normal,
                    color: isSystemReply ? Colors.grey[700] : Colors.black87,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildReplyInput() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.chat_bubble_outline, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Add a Reply',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _replyController,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'Type your reply here...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Theme.of(context).primaryColor, width: 2),
                ),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                const Spacer(),
                OutlinedButton(
                  onPressed: () {
                    _replyController.clear();
                  },
                  child: const Text('Clear'),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _isUpdating ? null : _addReply,
                  icon: _isUpdating
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.send, size: 18),
                  label: const Text('Send Reply'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
