# 🏘️ ResidenceHub

A comprehensive property management system built with Flutter and Supabase, designed to streamline the management of residential properties, tenants, and billing.

[![Flutter](https://img.shields.io/badge/Flutter-%2302569B.svg?style=flat&logo=Flutter&logoColor=white)](https://flutter.dev/)
[![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?style=flat&logo=supabase&logoColor=white)](https://supabase.io/)
[![Dart](https://img.shields.io/badge/Dart-%230175C2.svg?style=flat&logo=dart&logoColor=white)](https://dart.dev/)

## 🌟 Features

### Completed Features ✅

- **Property Management**
  - 🏢 Multiple property support
  - 🔑 Room/Unit management
  - ✨ Amenity tracking
  - 📝 Property details and documentation

- **Tenant Management**
  - 👥 Tenant profiles
  - 📋 Lease management
  - 🔐 Role-based access control
  - 👤 User settings

- **Billing System**
  - 💰 Bill generation
  - 📊 Bill templates
  - 🧾 Payment tracking
  - ⚡ Utility bill management
  - 💳 Multiple payment methods
  - 🔄 Direct support ticket creation from bills

- **Activity Tracking**
  - 📅 Activity logs
  - 👀 View management
  - 📈 Tenant views

- **Support Ticket System**
  - 🎫 Ticket creation and management
  - 🔄 Status tracking (In Progress, Resolved, Closed)
  - 🏷️ Category-based organization
  - 🚨 Priority levels with visual indicators
  - 💬 Ticket replies and communication
  - 👥 Role-based access control
  - 📱 Modern mobile-friendly UI

### Ongoing Development 🚧

- **Enhanced Reporting**
  - [ ] Advanced analytics dashboard
  - [ ] Custom report generation
  - [ ] Financial summaries

- **Communication Module**
  - [ ] In-app messaging
  - [ ] Notification system
  - [ ] Announcement board

## 🗺️ Roadmap

### Recently Completed ✅

- **Q2 2023**
  - ✅ Implemented comprehensive ticket management system
  - ✅ Enhanced UI with modern design patterns
  - ✅ Added direct support ticket creation from bills
  - ✅ Improved security with role-based access control
  - ✅ Implemented proper database views and RLS policies

### Upcoming 🚀

1. **Q3 2023**
   - Implement advanced reporting features
   - Add data visualization components
   - Enhance user interface

2. **Q4 2023**
   - Deploy communication module
   - Add real-time notifications
   - Implement chat functionality

3. **Q1 2024**
   - Launch maintenance management system
   - Add document management
   - Implement automated billing

## 🛠️ Tech Stack

- **Frontend**: Flutter
- **Backend**: Supabase
- **Database**: PostgreSQL
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage

## 📱 Supported Platforms

- ✅ Android
- ✅ iOS
- ✅ Web
- ✅ Windows
- ✅ macOS
- ✅ Linux

## 🚀 Getting Started

1. Clone the repository

```bash
git clone https://github.com/Thiararapeter/residencehub.git
```

2. Install dependencies

```bash
flutter pub get
```

3. Configure Supabase
   - Create a Supabase project
   - Update the configuration in `lib/config/supabase_config.dart`

4. Run the application

```bash
flutter run
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact

Peter Thiara - [@ThiaraPeter](https://github.com/Thiararapeter)

Project Link: [https://github.com/Thiararapeter/residencehub](https://github.com/Thiararapeter/residencehub)

---

⭐ Star this repo if you find it helpful!
