import 'package:flutter/material.dart';

enum TicketPriority {
  low,
  medium,
  high,
  urgent
}

enum TicketStatus {
  open,
  inProgress,
  resolved,
  closed
}

enum TicketCategory {
  maintenance,
  billing,
  noise,
  security,
  amenities,
  other
}

enum UserRole {
  tenant,
  admin,
  manager
}

class Ticket {
  final String id;
  final int ticketNumber;
  final String title;
  final String description;
  final TicketCategory category;
  final TicketPriority priority;
  TicketStatus status;
  final String createdBy;
  String? assignedTo;
  final DateTime createdAt;
  DateTime updatedAt;
  final String? createdByName;
  final String? createdByFirstName;
  final String? createdByLastName;
  final UserRole? createdByRole;
  final String? assignedToName;
  final String? assignedToFirstName;
  final String? assignedToLastName;
  final UserRole? assignedToRole;
  final bool hasReplies;

  Ticket({
    required this.id,
    required this.ticketNumber,
    required this.title,
    required this.description,
    required this.category,
    required this.priority,
    required this.status,
    required this.createdBy,
    this.assignedTo,
    required this.createdAt,
    required this.updatedAt,
    this.createdByName,
    this.createdByFirstName,
    this.createdByLastName,
    this.createdByRole,
    this.assignedToName,
    this.assignedToFirstName,
    this.assignedToLastName,
    this.assignedToRole,
    this.hasReplies = false,
  });

  factory Ticket.fromJson(Map<String, dynamic> json) {
    return Ticket(
      id: json['id'],
      ticketNumber: json['ticket_number'],
      title: json['title'],
      description: json['description'] ?? '',
      category: _categoryFromString(json['category']),
      priority: _priorityFromString(json['priority']),
      status: _statusFromString(json['status']),
      createdBy: json['created_by'],
      assignedTo: json['assigned_to'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      createdByName: json['created_by_name'],
      createdByFirstName: json['created_by_first_name'],
      createdByLastName: json['created_by_last_name'],
      createdByRole: _roleFromString(json['created_by_role']),
      assignedToName: json['assigned_to_name'],
      assignedToFirstName: json['assigned_to_first_name'],
      assignedToLastName: json['assigned_to_last_name'],
      assignedToRole: _roleFromString(json['assigned_to_role']),
      hasReplies: json['has_replies'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticket_number': ticketNumber,
      'title': title,
      'description': description,
      'category': category.name,
      'priority': priority.name,
      'status': status.name,
      'created_by': createdBy,
      'assigned_to': assignedTo,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toCreateJson() {
    return {
      'title': title,
      'description': description,
      'category': category.name,
      'priority': priority.name,
    };
  }

  static TicketCategory _categoryFromString(String category) {
    return TicketCategory.values.firstWhere(
      (e) => e.name == category,
      orElse: () => TicketCategory.other,
    );
  }

  static TicketPriority _priorityFromString(String priority) {
    return TicketPriority.values.firstWhere(
      (e) => e.name == priority,
      orElse: () => TicketPriority.medium,
    );
  }

  static TicketStatus _statusFromString(String status) {
    return TicketStatus.values.firstWhere(
      (e) => e.name == status.replaceAll(' ', ''),
      orElse: () => TicketStatus.open,
    );
  }
  
  static UserRole? _roleFromString(String? role) {
    if (role == null) return null;
    
    return UserRole.values.firstWhere(
      (e) => e.name == role,
      orElse: () => UserRole.tenant,
    );
  }

  Color getPriorityColor() {
    switch (priority) {
      case TicketPriority.low:
        return Colors.green;
      case TicketPriority.medium:
        return Colors.orange;
      case TicketPriority.high:
        return Colors.deepOrange;
      case TicketPriority.urgent:
        return Colors.red;
    }
  }

  Color getStatusColor() {
    switch (status) {
      case TicketStatus.open:
        return Colors.blue;
      case TicketStatus.inProgress:
        return Colors.amber;
      case TicketStatus.resolved:
        return Colors.green;
      case TicketStatus.closed:
        return Colors.grey;
    }
  }

  String getFormattedTicketNumber() {
    return 'Ticket - $ticketNumber';
  }

  String getStatusDisplay() {
    switch (status) {
      case TicketStatus.inProgress:
        return 'In Progress';
      default:
        return status.name[0].toUpperCase() + status.name.substring(1);
    }
  }
  
  bool canDelete(UserRole? currentUserRole) {
    return currentUserRole == UserRole.manager;
  }
  
  bool canUpdate(String currentUserId, UserRole? currentUserRole) {
    return currentUserId == createdBy || 
           currentUserId == assignedTo || 
           currentUserRole == UserRole.admin || 
           currentUserRole == UserRole.manager;
  }
  
  String getCreatedByDisplayName() {
    if (createdByName != null && createdByName!.isNotEmpty) {
      return createdByName!;
    }
    if (createdByFirstName != null && createdByLastName != null) {
      return '$createdByFirstName $createdByLastName';
    }
    if (createdByFirstName != null) {
      return createdByFirstName!;
    }
    return 'Unknown';
  }
  
  String getAssignedToDisplayName() {
    if (assignedToName != null && assignedToName!.isNotEmpty) {
      return assignedToName!;
    }
    if (assignedToFirstName != null && assignedToLastName != null) {
      return '$assignedToFirstName $assignedToLastName';
    }
    if (assignedToFirstName != null) {
      return assignedToFirstName!;
    }
    return 'Unassigned';
  }
  
  String getCreatedByFirstName() {
    if (createdByFirstName != null && createdByFirstName!.isNotEmpty) {
      return createdByFirstName!;
    }
    if (createdByName != null && createdByName!.isNotEmpty) {
      // Extract first name from display name
      final parts = createdByName!.split(' ');
      return parts.isNotEmpty ? parts.first : 'Unknown';
    }
    return 'Unknown';
  }
}
