import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/tenant_service.dart';
import '../services/bill_service.dart';
import '../models/tenant_housing.dart';
import '../widgets/theme_toggle.dart';
import '../widgets/welcome_card.dart';
import '../widgets/bills_summary_card.dart';
import '../widgets/quick_actions_grid.dart';
import '../theme/index.dart';
import 'dart:async';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Timer _timer;
  late DateTime _currentTime;
  final AuthService authService = AuthService();
  final TenantService tenantService = TenantService();
  final BillService billService = BillService();

  // Cache for tenant details to prevent constant reloading
  Future<Map<String, dynamic>?>? _tenantDetailsFuture;
  Future<TenantHousing?>? _housingDetailsFuture;
  Future<Map<String, dynamic>>? _billsSummaryFuture;

  @override
  void initState() {
    super.initState();
    _currentTime = DateTime.now();
    // Update time every minute instead of every second to reduce rebuilds
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      setState(() {
        _currentTime = DateTime.now();
      });
    });

    // Initialize cached futures
    _loadData();
  }

  void _loadData() {
    final userId = authService.currentUser?.id;
    _tenantDetailsFuture = tenantService.getTenantDetails(userId);
    _housingDetailsFuture = tenantService.getTenantHousingDetails(userId);
    _billsSummaryFuture = billService.getBillsSummary();
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Residence Hub'),
        centerTitle: true,
        actions: [
          const ThemeSelector(),
        ],
      ),
      drawer: FutureBuilder<Map<String, dynamic>?>(
        future: _tenantDetailsFuture,
        builder: (context, tenantSnapshot) {
          // Get user name and email from tenant data
          final String userName = tenantSnapshot.data?['full_name'] ?? 'Resident';
          
          return FutureBuilder<TenantHousing?>(
            future: _housingDetailsFuture,
            builder: (context, housingSnapshot) {
              // Get property and room details
              final String propertyName = housingSnapshot.data?.room?.property?.name ?? 'Property';
              final String roomName = housingSnapshot.data?.room?.name ?? 'Room';
              
              return Drawer(
                child: Column(
                  children: <Widget>[
                    Container(
                      padding: const EdgeInsets.only(top: 40, bottom: 20, left: 16, right: 16),
                      decoration: const BoxDecoration(
                        color: Color(0xFF2A6FDB),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: Colors.white,
                                radius: 30,
                                child: Text(
                                  userName.isNotEmpty ? userName[0].toUpperCase() : 'R',
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF2A6FDB),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      userName,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                        color: Colors.white,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      "Loggedin User: ${tenantSnapshot.data?['first_name'] ?? 'Resident'}",
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.white70,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      "$propertyName - $roomName",
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.white70,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: <Widget>[
                          ListTile(
                            leading: const Icon(Icons.home),
                            title: const Text('Home'),
                            onTap: () {
                              Navigator.pop(context);
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.meeting_room),
                            title: const Text('Room Details'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/housing');
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.receipt_long),
                            title: const Text('Bills'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/bills');
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.bolt),
                            title: const Text('Utility Bills'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/utility-bills');
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.support),
                            title: const Text('Tickets'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/tickets');
                            },
                          ),
                          ListTile(
                            leading: const Icon(Icons.settings),
                            title: const Text('Settings'),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pushNamed(context, '/settings');
                            },
                          ),
                        ],
                      ),
                    ),
                    const Divider(),
                    ListTile(
                      leading: const Icon(Icons.person),
                      title: const Text('Profile'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, '/profile');
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.logout),
                      title: const Text('Logout'),
                      onTap: () async {
                        Navigator.pop(context);
                        await authService.signOut();
                        if (context.mounted) {
                          Navigator.pushReplacementNamed(context, '/login');
                        }
                      },
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              );
            },
          );
        },
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          setState(() {
            _loadData();
          });
        },
        child: Padding(
          padding: AppThemeConstants.screenPadding,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Card with Date, Time, and Property Info
                FutureBuilder<TenantHousing?>(
                  future: _housingDetailsFuture,
                  builder: (context, housingSnapshot) {
                    return FutureBuilder<Map<String, dynamic>?>(
                      future: _tenantDetailsFuture,
                      builder: (context, tenantSnapshot) {
                        final String userName =
                            tenantSnapshot.data?['first_name'] ??
                            tenantSnapshot.data?['full_name']
                                ?.toString()
                                .split(' ')
                                .first ??
                            'Resident';

                        // Debug logging
                        debugPrint('HOME_SCREEN: Housing snapshot data: ${housingSnapshot.data}');
                        debugPrint('HOME_SCREEN: Housing snapshot hasData: ${housingSnapshot.hasData}');
                        debugPrint('HOME_SCREEN: Housing snapshot connectionState: ${housingSnapshot.connectionState}');
                        debugPrint('HOME_SCREEN: Housing snapshot error: ${housingSnapshot.error}');
                        debugPrint('HOME_SCREEN: Tenant snapshot data: ${tenantSnapshot.data}');
                        debugPrint('HOME_SCREEN: User name: $userName');

                        return WelcomeCard(
                          userName: userName,
                          currentTime: _currentTime,
                          housingDetails: housingSnapshot.data,
                        );
                      },
                    );
                  },
                ),

                const SizedBox(height: 20),
                
                // Bills Summary Section
                FutureBuilder<Map<String, dynamic>>(
                  future: _billsSummaryFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppThemeConstants.radiusL),
                        ),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        ),
                      );
                    }

                    return BillsSummaryCard(
                      summary: snapshot.data ?? {},
                    );
                  },
                ),

                const SizedBox(height: 20),

                // Quick Actions Grid
                const QuickActionsGrid(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
