-- Add closed_reason column if it doesn't exist
ALTER TABLE tickets 
ADD COLUMN IF NOT EXISTS closed_reason TEXT;

-- <PERSON>reate function to check and close inactive tickets with fixed search path
CREATE OR REPLACE FUNCTION close_inactive_tickets() RETURNS void 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql AS $$
BEGIN
    -- Update tickets that have been inactive for 7 days
    UPDATE tickets
    SET 
        status = 'closed',
        updated_at = NOW(),
        closed_reason = 'Automatically closed due to 7 days of inactivity'
    WHERE
        status NOT IN ('closed', 'resolved')
        AND (
            -- No replies for 7 days
            (
                id NOT IN (SELECT DISTINCT ticket_id FROM ticket_replies WHERE created_at > NOW() - INTERVAL '7 days')
                AND updated_at < NOW() - INTERVAL '7 days'
            )
            -- Or no activity at all for 7 days
            OR (
                updated_at < NOW() - INTERVAL '7 days'
            )
        );
END;
$$;

-- Create a trigger to run daily with fixed search path
CREATE OR REPLACE FUNCTION trigger_close_inactive_tickets() <PERSON><PERSON><PERSON><PERSON> trigger 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql AS $$
BEGIN
    -- Only run once per day
    IF EXISTS (
        SELECT 1 FROM tickets 
        WHERE updated_at > NOW() - INTERVAL '24 hours'
        AND status = 'closed' 
        AND closed_reason = 'Automatically closed due to 7 days of inactivity'
    ) THEN
        RETURN NULL;
    END IF;
    
    PERFORM close_inactive_tickets();
    RETURN NULL;
END;
$$;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS check_inactive_tickets ON tickets;
DROP TRIGGER IF EXISTS check_inactive_tickets_on_reply ON ticket_replies;

-- Create trigger to run on any ticket activity
CREATE TRIGGER check_inactive_tickets
AFTER INSERT OR UPDATE ON tickets
FOR EACH STATEMENT
EXECUTE FUNCTION trigger_close_inactive_tickets();

-- Create trigger to run on any ticket reply
CREATE TRIGGER check_inactive_tickets_on_reply
AFTER INSERT OR UPDATE ON ticket_replies
FOR EACH STATEMENT
EXECUTE FUNCTION trigger_close_inactive_tickets();

-- Remove unused indexes first
DROP INDEX IF EXISTS idx_tickets_assigned_to;
DROP INDEX IF EXISTS idx_tickets_created_by;
DROP INDEX IF EXISTS idx_tickets_updated_at;
DROP INDEX IF EXISTS idx_tickets_status;
DROP INDEX IF EXISTS idx_active_tickets;
DROP INDEX IF EXISTS idx_closed_tickets_with_reason;
DROP INDEX IF EXISTS idx_ticket_replies_created_at;
DROP INDEX IF EXISTS idx_ticket_replies_user_id;

-- Create optimized composite indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tickets_status_updated
ON tickets(status, updated_at);

CREATE INDEX IF NOT EXISTS idx_tickets_tenant_status
ON tickets(tenant_id, status);

CREATE INDEX IF NOT EXISTS idx_tickets_creator_status
ON tickets(created_by, status);

CREATE INDEX IF NOT EXISTS idx_ticket_replies_ticket_created
ON ticket_replies(ticket_id, created_at);

-- Fix the RLS policy to avoid re-evaluating auth functions for each row
DROP POLICY IF EXISTS "tickets_select_policy" ON tickets;
CREATE POLICY "tickets_select_policy"
    ON tickets FOR SELECT
    USING (
        (SELECT has_valid_role()) AND (
            (SELECT auth.uid()) = created_by 
            OR (SELECT auth.uid()) = assigned_to 
            OR (SELECT can_manage_all_tickets())
            OR EXISTS (
                SELECT 1 FROM tenants t
                WHERE t.id = tickets.tenant_id 
                AND t.user_id = (SELECT auth.uid())
            )
        )
    );

-- Add RLS policy for the closed_reason column
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;

-- Add documentation
COMMENT ON FUNCTION close_inactive_tickets() IS 'Automatically closes tickets that have been inactive for 7 days. A ticket is considered inactive if it has no replies and no updates for 7 days.';
COMMENT ON FUNCTION trigger_close_inactive_tickets() IS 'Trigger function that runs the auto-close check. Includes rate limiting to run at most once per day.';
COMMENT ON COLUMN tickets.closed_reason IS 'Reason for ticket closure. Set automatically for system-closed tickets.';

-- Fix the ensure_user_profile function search path issue for both function signatures
ALTER FUNCTION ensure_user_profile() SET search_path = public;
ALTER FUNCTION ensure_user_profile(user_id uuid, user_email text, user_metadata jsonb) SET search_path = public; 