import 'package:flutter/material.dart';

enum ReplyType {
  comment,
  statusUpdate,
  assignment
}

class TicketReply {
  final String id;
  final String ticketId;
  final String userId;
  final String message;
  final ReplyType replyType;
  final bool isInternal;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? userName;
  final String? userFirstName;
  final String? userLastName;
  final String? userRole;

  TicketReply({
    required this.id,
    required this.ticketId,
    required this.userId,
    required this.message,
    required this.replyType,
    required this.isInternal,
    required this.createdAt,
    required this.updatedAt,
    this.userName,
    this.userFirstName,
    this.userLastName,
    this.userRole,
  });

  factory TicketReply.fromJson(Map<String, dynamic> json) {
    return TicketReply(
      id: json['id'],
      ticketId: json['ticket_id'],
      userId: json['user_id'],
      message: json['message'] ?? '',
      replyType: _replyTypeFromString(json['reply_type']),
      isInternal: json['is_internal'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      userName: json['user_name'],
      userFirstName: json['user_first_name'],
      userLastName: json['user_last_name'],
      userRole: json['user_role'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticket_id': ticketId,
      'user_id': userId,
      'message': message,
      'reply_type': replyType.name,
      'is_internal': isInternal,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toCreateJson() {
    return {
      'ticket_id': ticketId,
      'user_id': userId,
      'message': message,
      'reply_type': replyType.name,
      'is_internal': isInternal,
    };
  }

  static ReplyType _replyTypeFromString(String? replyType) {
    switch (replyType) {
      case 'status_update':
        return ReplyType.statusUpdate;
      case 'assignment':
        return ReplyType.assignment;
      case 'comment':
      default:
        return ReplyType.comment;
    }
  }

  String getDisplayName() {
    if (userName != null && userName!.isNotEmpty) {
      return userName!;
    }
    if (userFirstName != null && userLastName != null) {
      return '$userFirstName $userLastName';
    }
    if (userFirstName != null) {
      return userFirstName!;
    }
    return 'User';
  }

  Color getReplyTypeColor() {
    switch (replyType) {
      case ReplyType.statusUpdate:
        return Colors.blue;
      case ReplyType.assignment:
        return Colors.purple;
      case ReplyType.comment:
      default:
        return Colors.grey;
    }
  }

  IconData getReplyTypeIcon() {
    switch (replyType) {
      case ReplyType.statusUpdate:
        return Icons.update;
      case ReplyType.assignment:
        return Icons.person_add;
      case ReplyType.comment:
      default:
        return Icons.chat_bubble_outline;
    }
  }

  String getFormattedTime() {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  bool isSystemReply() {
    return replyType == ReplyType.statusUpdate || replyType == ReplyType.assignment;
  }
}
