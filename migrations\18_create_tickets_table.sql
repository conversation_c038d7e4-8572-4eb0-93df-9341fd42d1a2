DROP VIEW IF EXISTS admin_ticket_replies CASCADE;
DROP VIEW IF EXISTS tenant_ticket_replies CASCADE;
DROP VIEW IF EXISTS ticket_replies_details CASCADE;
DROP VIEW IF EXISTS admin_tickets CASCADE;
DROP VIEW IF EXISTS tenant_tickets CASCADE;
DROP VIEW IF EXISTS ticket_details CASCADE;

-- Drop triggers (only if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tickets' AND table_schema = 'public') THEN
        DROP TRIGGER IF EXISTS ticket_status_change_trigger ON tickets;
        DROP TRIGGER IF EXISTS set_ticket_number_trigger ON tickets;
        DROP TRIGGER IF EXISTS log_ticket_creation_trigger ON tickets;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ticket_replies' AND table_schema = 'public') THEN
        DROP TRIGGER IF EXISTS update_ticket_on_reply_trigger ON ticket_replies;
    END IF;
END
$$;

-- Drop functions with CASCADE to handle dependencies
DROP FUNCTION IF EXISTS get_latest_ticket_reply(UUID) CASCADE;
DROP FUNCTION IF EXISTS get_ticket_reply_count(UUID) CASCADE;
DROP FUNCTION IF EXISTS create_status_change_reply() CASCADE;
DROP FUNCTION IF EXISTS update_ticket_on_reply() CASCADE;
DROP FUNCTION IF EXISTS get_user_display_name(UUID) CASCADE;
DROP FUNCTION IF EXISTS can_manage_all_tickets() CASCADE;
DROP FUNCTION IF EXISTS set_ticket_number() CASCADE;
DROP FUNCTION IF EXISTS log_ticket_creation() CASCADE;

-- Drop policies (only if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tickets' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS tickets_delete_policy ON tickets;
        DROP POLICY IF EXISTS tickets_update_policy ON tickets;
        DROP POLICY IF EXISTS tickets_insert_policy ON tickets;
        DROP POLICY IF EXISTS tickets_select_policy ON tickets;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'ticket_replies' AND table_schema = 'public') THEN
        DROP POLICY IF EXISTS ticket_replies_delete_policy ON ticket_replies;
        DROP POLICY IF EXISTS ticket_replies_update_policy ON ticket_replies;
        DROP POLICY IF EXISTS ticket_replies_insert_policy ON ticket_replies;
        DROP POLICY IF EXISTS ticket_replies_select_policy ON ticket_replies;
    END IF;
END
$$;

-- Drop indexes (they will be dropped with tables)

-- Drop tables
DROP TABLE IF EXISTS ticket_replies CASCADE;
DROP TABLE IF EXISTS tickets CASCADE;

-- Drop sequence
DROP SEQUENCE IF EXISTS ticket_number_seq CASCADE;

-- =============================================
-- CREATE TABLES
-- =============================================

-- Create tickets table with auto-incrementing ticket number
CREATE TABLE tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_number BIGINT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    priority TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'open',
    created_by UUID NOT NULL REFERENCES auth.users(id),
    assigned_to UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create ticket_replies table for tenant communication
CREATE TABLE ticket_replies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    ticket_id UUID NOT NULL REFERENCES tickets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    message TEXT NOT NULL,
    reply_type TEXT NOT NULL DEFAULT 'comment', -- 'comment', 'status_update', 'assignment'
    is_internal BOOLEAN DEFAULT false, -- For admin/manager internal notes
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for ticket_replies
CREATE INDEX idx_ticket_replies_ticket_id ON ticket_replies(ticket_id);
CREATE INDEX idx_ticket_replies_created_at ON ticket_replies(created_at);
CREATE INDEX idx_ticket_replies_user_id ON ticket_replies(user_id);

-- Create a sequence for ticket numbers starting at 1000001
CREATE SEQUENCE ticket_number_seq START 1000001;

-- Create function to automatically set the ticket number
CREATE OR REPLACE FUNCTION set_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    NEW.ticket_number := nextval('ticket_number_seq');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Create trigger to set ticket number before insert
CREATE TRIGGER set_ticket_number_trigger
BEFORE INSERT ON tickets
FOR EACH ROW
EXECUTE FUNCTION set_ticket_number();

-- Ensure user_profiles has display name fields for secure user identification
ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS first_name TEXT;
ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS last_name TEXT;
ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS display_name TEXT;
ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'tenant';

-- Create function to get safe display name for users
CREATE OR REPLACE FUNCTION get_user_display_name(user_id UUID)
RETURNS TEXT AS $$
DECLARE
    result TEXT;
BEGIN
    SELECT 
        CASE 
            WHEN display_name IS NOT NULL AND display_name != '' THEN display_name
            WHEN first_name IS NOT NULL AND last_name IS NOT NULL THEN first_name || ' ' || last_name
            WHEN first_name IS NOT NULL THEN first_name
            ELSE 'User'
        END
    INTO result
    FROM public.user_profiles 
    WHERE id = user_id;
    
    RETURN COALESCE(result, 'Unknown User');
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Create a function to check user role for ticket operations
CREATE OR REPLACE FUNCTION can_manage_all_tickets()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_profiles
        WHERE id = (select auth.uid()) AND (role = 'admin' OR role = 'manager')
    );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Create a function to check if user has a valid role (tenant, admin, or manager)
CREATE OR REPLACE FUNCTION has_valid_role()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.user_profiles
        WHERE id = (select auth.uid()) AND (role = 'tenant' OR role = 'admin' OR role = 'manager')
    );
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Function to update ticket's updated_at when a reply is added
CREATE OR REPLACE FUNCTION update_ticket_on_reply()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the ticket's updated_at timestamp
    UPDATE tickets 
    SET updated_at = now() 
    WHERE id = NEW.ticket_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Function to automatically create system replies for status changes
CREATE OR REPLACE FUNCTION create_status_change_reply()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create reply if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO ticket_replies (
            ticket_id,
            user_id,
            message,
            reply_type
        ) VALUES (
            NEW.id,
            (select auth.uid()),
            'Status changed from "' ||
            CASE OLD.status
                WHEN 'inProgress' THEN 'In Progress'
                ELSE INITCAP(OLD.status)
            END || '" to "' || 
            CASE NEW.status
                WHEN 'inProgress' THEN 'In Progress'
                ELSE INITCAP(NEW.status)
            END || '"',
            'status_update'
        );
    END IF;
    
    -- Create reply if assignment changed
    IF OLD.assigned_to IS DISTINCT FROM NEW.assigned_to THEN
        INSERT INTO ticket_replies (
            ticket_id,
            user_id,
            message,
            reply_type
        ) VALUES (
            NEW.id,
            (select auth.uid()),
            CASE
                WHEN NEW.assigned_to IS NULL THEN 'Ticket unassigned'
                WHEN OLD.assigned_to IS NULL THEN 'Ticket assigned to ' || get_user_display_name(NEW.assigned_to)
                ELSE 'Ticket reassigned to ' || get_user_display_name(NEW.assigned_to)
            END,
            'assignment'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Function to get reply count for a ticket
CREATE OR REPLACE FUNCTION get_ticket_reply_count(ticket_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    reply_count INTEGER;
BEGIN
    SELECT COUNT(*)
    INTO reply_count
    FROM ticket_replies tr
    WHERE tr.ticket_id = ticket_uuid
    AND NOT tr.is_internal; -- Don't count internal replies for public count
    
    RETURN COALESCE(reply_count, 0);
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Function to get latest reply for a ticket
CREATE OR REPLACE FUNCTION get_latest_ticket_reply(ticket_uuid UUID)
RETURNS TABLE(
    reply_id UUID,
    message TEXT,
    user_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tr.id,
        tr.message,
        up.display_name,
        tr.created_at
    FROM ticket_replies tr
    LEFT JOIN public.user_profiles up ON tr.user_id = up.id
    WHERE tr.ticket_id = ticket_uuid
    AND NOT tr.is_internal
    ORDER BY tr.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Function to log ticket creation attempts - simplified version that avoids activity_logs
CREATE OR REPLACE FUNCTION log_ticket_creation()
RETURNS TRIGGER AS $$
BEGIN
    -- We're not using activity_logs anymore to avoid schema compatibility issues
    -- This function now just returns the NEW record without doing any logging
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY INVOKER SET search_path = public;

-- Create secure view for tickets with role-based information
CREATE VIEW ticket_details WITH (security_invoker=true) AS
SELECT 
    t.id,
    t.ticket_number,
    t.title,
    t.description,
    t.category,
    t.priority,
    t.status,
    t.created_at,
    t.updated_at,
    t.created_by,
    t.assigned_to,
    p1.display_name as created_by_name,
    p1.first_name as created_by_first_name,
    p1.last_name as created_by_last_name,
    p1.role as created_by_role,
    p2.display_name as assigned_to_name,
    p2.first_name as assigned_to_first_name,
    p2.last_name as assigned_to_last_name,
    p2.role as assigned_to_role
FROM 
    tickets t
LEFT JOIN
    public.user_profiles p1 ON t.created_by = p1.id
LEFT JOIN
    public.user_profiles p2 ON t.assigned_to = p2.id;

-- Create secure view for ticket replies with user information
CREATE VIEW ticket_replies_details WITH (security_invoker=true) AS
SELECT 
    tr.id,
    tr.ticket_id,
    tr.user_id,
    tr.message,
    tr.reply_type,
    tr.is_internal,
    tr.created_at,
    tr.updated_at,
    up.display_name as user_name,
    up.first_name as user_first_name,
    up.last_name as user_last_name,
    up.role as user_role
FROM 
    ticket_replies tr
LEFT JOIN
    public.user_profiles up ON tr.user_id = up.id
ORDER BY tr.created_at ASC;

-- Create secure view for admins and managers to see all tickets
CREATE VIEW admin_tickets WITH (security_invoker=true) AS
SELECT *
FROM ticket_details
WHERE has_valid_role() AND can_manage_all_tickets();

-- Create secure view for tenants to see only their tickets
CREATE VIEW tenant_tickets WITH (security_invoker=true) AS
SELECT *
FROM ticket_details
WHERE has_valid_role() AND (created_by = (select auth.uid()) OR assigned_to = (select auth.uid()));

-- Create view for tenants to see only non-internal replies
CREATE VIEW tenant_ticket_replies WITH (security_invoker=true) AS
SELECT *
FROM ticket_replies_details
WHERE has_valid_role() AND NOT is_internal
AND EXISTS (
    SELECT 1 FROM tickets t
    WHERE t.id = ticket_replies_details.ticket_id
    AND (t.created_by = (select auth.uid()) OR t.assigned_to = (select auth.uid()))
);

-- Create view for admins/managers to see all replies
CREATE VIEW admin_ticket_replies WITH (security_invoker=true) AS
SELECT *
FROM ticket_replies_details
WHERE has_valid_role() AND can_manage_all_tickets();

-- Add RLS policies
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;

-- Policy to allow tenants to view their own tickets
-- Admins and managers can view all tickets
CREATE POLICY tickets_select_policy ON tickets 
    FOR SELECT 
    USING (
        has_valid_role() AND (
            (select auth.uid()) = created_by 
            OR (select auth.uid()) = assigned_to
            OR can_manage_all_tickets()
        )
    );

-- Policy to allow users to insert their own tickets
CREATE POLICY tickets_insert_policy ON tickets 
    FOR INSERT 
    WITH CHECK (
        has_valid_role() AND
        (select auth.uid()) = created_by
    );

-- Add helpful comment explaining the insert policy
COMMENT ON POLICY tickets_insert_policy ON tickets IS 
'Allows authenticated users with valid roles (tenant, admin, manager) to create tickets where they are the creator. 
This policy checks that the created_by field matches the authenticated user''s ID.';

-- Policy to allow tenants to update tickets they created
-- Admins and managers can update any ticket
CREATE POLICY tickets_update_policy ON tickets 
    FOR UPDATE 
    USING (
        has_valid_role() AND (
            (select auth.uid()) = created_by
            OR (select auth.uid()) = assigned_to
            OR can_manage_all_tickets()
        )
    );

-- Policy to allow only managers to delete tickets
CREATE POLICY tickets_delete_policy ON tickets
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles
            WHERE id = (select auth.uid()) AND role = 'manager'
        )
    );

-- Enable RLS for ticket_replies
ALTER TABLE ticket_replies ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can view replies for tickets they have access to
-- Tenants cannot see internal replies
CREATE POLICY ticket_replies_select_policy ON ticket_replies 
    FOR SELECT 
    USING (
        has_valid_role() AND (
            EXISTS (
                SELECT 1 FROM tickets t
                WHERE t.id = ticket_replies.ticket_id
                AND (
                    (select auth.uid()) = t.created_by 
                    OR (select auth.uid()) = t.assigned_to
                    OR can_manage_all_tickets()
                )
            )
            AND (
                -- Hide internal replies from non-admin users
                NOT is_internal OR can_manage_all_tickets()
            )
        )
    );

-- RLS Policy: Users can insert replies for tickets they have access to
-- Non-admin users cannot create internal replies
CREATE POLICY ticket_replies_insert_policy ON ticket_replies 
    FOR INSERT 
    WITH CHECK (
        has_valid_role() AND
        (select auth.uid()) = user_id
        AND EXISTS (
            SELECT 1 FROM tickets t
            WHERE t.id = ticket_replies.ticket_id
            AND (
                (select auth.uid()) = t.created_by 
                OR (select auth.uid()) = t.assigned_to
                OR can_manage_all_tickets()
            )
        )
        AND (
            -- Non-admin users cannot create internal replies
            NOT is_internal OR can_manage_all_tickets()
        )
    );

-- RLS Policy: Users can update their own replies
-- Admins can update any reply
CREATE POLICY ticket_replies_update_policy ON ticket_replies 
    FOR UPDATE 
    USING (
        has_valid_role() AND (
            (select auth.uid()) = user_id
            OR can_manage_all_tickets()
        )
    );

-- RLS Policy: Only managers can delete replies
CREATE POLICY ticket_replies_delete_policy ON ticket_replies
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_profiles
            WHERE id = (select auth.uid()) AND role = 'manager'
        )
    );

-- Create triggers
-- Trigger to update ticket timestamp when reply is added
CREATE TRIGGER update_ticket_on_reply_trigger
    AFTER INSERT ON ticket_replies
    FOR EACH ROW
    EXECUTE FUNCTION update_ticket_on_reply();

-- Create trigger to automatically log status and assignment changes
CREATE TRIGGER ticket_status_change_trigger
    AFTER UPDATE ON tickets
    FOR EACH ROW
    EXECUTE FUNCTION create_status_change_reply();

-- Create trigger for logging ticket creation attempts
CREATE TRIGGER log_ticket_creation_trigger
    AFTER INSERT ON tickets
    FOR EACH ROW
    EXECUTE FUNCTION log_ticket_creation();

-- Ensure all authenticated users have a user_profiles entry
-- This is critical because the RLS policy relies on user_profiles for role checking
INSERT INTO public.user_profiles (id, role)
SELECT id, 'tenant'
FROM auth.users u
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_profiles p WHERE p.id = u.id
);

-- Update existing user_profiles with display names if available
DO $$
BEGIN
    -- Try to populate display names from existing profiles table if it exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles' AND table_schema = 'public') THEN
        UPDATE public.user_profiles 
        SET 
            display_name = COALESCE(p.full_name, 'User'),
            first_name = SPLIT_PART(p.full_name, ' ', 1),
            last_name = CASE 
                WHEN array_length(string_to_array(p.full_name, ' '), 1) > 1 
                THEN array_to_string((string_to_array(p.full_name, ' '))[2:], ' ')
                ELSE NULL 
                END
        FROM public.profiles p 
        WHERE public.user_profiles.id = p.id 
        AND public.user_profiles.display_name IS NULL;
    END IF;
    
    -- For users without display names, set a default
    UPDATE public.user_profiles 
    SET display_name = 'User ' || substring(id::text, 1, 8)
    WHERE display_name IS NULL OR display_name = '';
END
$$;