import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/auth_service.dart';
import '../services/tenant_service.dart';
import '../theme/index.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final AuthService _authService = AuthService();
  final TenantService _tenantService = TenantService();

  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _emergencyNameController = TextEditingController();
  final _emergencyPhoneController = TextEditingController();

  bool _isLoading = false;
  bool _isEditing = false;
  Map<String, dynamic>? _tenantData;

  @override
  void initState() {
    super.initState();
    _loadTenantData();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _emergencyNameController.dispose();
    _emergencyPhoneController.dispose();
    super.dispose();
  }

  Future<void> _loadTenantData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUser?.id;
      final data = await _tenantService.getTenantDetails(userId);

      if (!mounted) return;

      setState(() {
        _tenantData = data;

        // Initialize controllers with existing data
        if (data != null) {
          _phoneController.text = data['phone_number'] ?? '';
          _emergencyNameController.text = data['emergency_contact_name'] ?? '';
          _emergencyPhoneController.text =
              data['emergency_contact_phone'] ?? '';
        }
      });
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error loading profile: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUser?.id;
      if (userId == null || _tenantData == null) {
        throw Exception('User not authenticated or tenant data not found');
      }

      // Update tenant data
      await _supabase
          .from('tenants')
          .update({
            'phone_number': _phoneController.text,
            'emergency_contact_name': _emergencyNameController.text,
            'emergency_contact_phone': _emergencyPhoneController.text,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', _tenantData!['id']);

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Profile updated successfully')),
      );

      // Reload tenant data
      await _loadTenantData();

      if (!mounted) return;

      setState(() {
        _isEditing = false;
      });
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error updating profile: $e')));
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  final _supabase = Supabase.instance.client;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => setState(() => _isEditing = true),
              tooltip: 'Edit Profile',
            )
          else
            IconButton(
              icon: const Icon(Icons.cancel),
              onPressed: () => setState(() => _isEditing = false),
              tooltip: 'Cancel',
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _tenantData == null
              ? const Center(child: Text('No profile data found'))
              : SingleChildScrollView(
                  padding: AppThemeConstants.screenPadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildProfileHeader(),
                      const SizedBox(height: 24),
                      _isEditing ? _buildEditForm() : _buildProfileDetails(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildProfileHeader() {
    return Center(
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: Text(
              _getInitials(),
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            '${_tenantData!['first_name'] ?? ''} ${_tenantData!['last_name'] ?? ''}',
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _tenantData!['email'] ?? '',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          if (_tenantData!['status'] != null)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor().withAlpha(30),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                _tenantData!['status'].toString().toUpperCase(),
                style: TextStyle(
                  color: _getStatusColor(),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileDetails() {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Personal Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Divider(),
            _buildInfoRow(
              'Phone',
              _tenantData!['phone_number'] ?? 'Not provided',
            ),

            if (_tenantData!['lease_start_date'] != null)
              _buildInfoRow(
                'Lease Start',
                DateFormat(
                  'MMM d, yyyy',
                ).format(DateTime.parse(_tenantData!['lease_start_date'])),
              ),

            if (_tenantData!['lease_end_date'] != null)
              _buildInfoRow(
                'Lease End',
                DateFormat(
                  'MMM d, yyyy',
                ).format(DateTime.parse(_tenantData!['lease_end_date'])),
              ),

            const SizedBox(height: 24),
            Text(
              'Emergency Contact',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const Divider(),
            _buildInfoRow(
              'Name',
              _tenantData!['emergency_contact_name'] ?? 'Not provided',
            ),
            _buildInfoRow(
              'Phone',
              _tenantData!['emergency_contact_phone'] ?? 'Not provided',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditForm() {
    return Form(
      key: _formKey,
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Edit Profile',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Divider(),

              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  hintText: 'Enter your phone number',
                ),
                keyboardType: TextInputType.phone,
              ),

              const SizedBox(height: 24),
              Text(
                'Emergency Contact',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Divider(),

              TextFormField(
                controller: _emergencyNameController,
                decoration: const InputDecoration(
                  labelText: 'Emergency Contact Name',
                  hintText: 'Enter emergency contact name',
                ),
              ),

              const SizedBox(height: 16),

              TextFormField(
                controller: _emergencyPhoneController,
                decoration: const InputDecoration(
                  labelText: 'Emergency Contact Phone',
                  hintText: 'Enter emergency contact phone',
                ),
                keyboardType: TextInputType.phone,
              ),

              const SizedBox(height: 24),

              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _updateProfile,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : const Text('Save Changes'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value, style: Theme.of(context).textTheme.bodyMedium),
          ),
        ],
      ),
    );
  }

  String _getInitials() {
    final firstName = _tenantData!['first_name'] as String? ?? '';
    final lastName = _tenantData!['last_name'] as String? ?? '';

    String initials = '';
    if (firstName.isNotEmpty) initials += firstName[0];
    if (lastName.isNotEmpty) initials += lastName[0];

    return initials.toUpperCase();
  }

  Color _getStatusColor() {
    final status = _tenantData!['status'] as String? ?? '';

    switch (status.toLowerCase()) {
      case 'active':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'inactive':
        return Colors.red;
      case 'movedout':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }
}
