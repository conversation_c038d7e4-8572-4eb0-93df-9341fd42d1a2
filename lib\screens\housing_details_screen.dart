import 'package:flutter/material.dart';
import '../models/tenant_housing.dart';
import '../services/tenant_service.dart';
import '../services/auth_service.dart';
import '../theme/index.dart';
import '../utils/currency_formatter.dart';

class HousingDetailsScreen extends StatefulWidget {
  const HousingDetailsScreen({super.key});

  @override
  State<HousingDetailsScreen> createState() => _HousingDetailsScreenState();
}

class _HousingDetailsScreenState extends State<HousingDetailsScreen> {
  final TenantService _tenantService = TenantService();
  final AuthService _authService = AuthService();
  late Future<TenantHousing?> _housingDetailsFuture;

  @override
  void initState() {
    super.initState();
    _housingDetailsFuture = _loadHousingDetails();
  }

  Future<TenantHousing?> _loadHousingDetails() async {
    final userId = _authService.currentUser?.id;
    return await _tenantService.getTenantHousingDetails(userId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Housing Details')),
      body: FutureBuilder<TenantHousing?>(
        future: _housingDetailsFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error loading housing details: ${snapshot.error}',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            );
          } else if (snapshot.data == null) {
            return Center(
              child: Text(
                'No housing details found',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            );
          } else {
            final housing = snapshot.data!;
            return _buildHousingDetails(housing);
          }
        },
      ),
    );
  }

  Widget _buildHousingDetails(TenantHousing housing) {
    return SingleChildScrollView(
      padding: AppThemeConstants.screenPadding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property information (if available)
          if (housing.room?.property != null) ...[
            Text(
              'Property Information',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            _buildInfoCard(
              title: housing.room!.property!.name,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow(
                    'Address',
                    housing.room!.property!.fullAddress,
                  ),
                  if (housing.room!.property!.description != null)
                    _buildInfoRow(
                      'Description',
                      housing.room!.property!.description!,
                    ),
                ],
              ),
            ),
          ],

          // Tenant information
          const SizedBox(height: 24),
          Text(
            'Personal Details',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          _buildInfoCard(
            title: housing.fullName,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('Name', housing.fullName),
                _buildInfoRow('Email', housing.email),
                if (housing.phoneNumber != null)
                  _buildInfoRow('Phone', housing.phoneNumber!),
                if (housing.room?.property != null)
                  _buildInfoRow('Property', housing.room!.property!.name),
                if (housing.room != null)
                  _buildInfoRow('Room Number', housing.room!.name),
                if (housing.emergencyContactName != null)
                  _buildInfoRow(
                    'Emergency Contact',
                    housing.emergencyContactName!,
                  ),
                if (housing.emergencyContactPhone != null)
                  _buildInfoRow(
                    'Emergency Phone',
                    housing.emergencyContactPhone!,
                  ),
              ],
            ),
          ),

          // Room information (if available)
          if (housing.room != null) ...[
            const SizedBox(height: 24),
            Text(
              'Room Information',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            _buildInfoCard(
              title: housing.room!.name,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoRow(
                    'Room Type',
                    housing.room!.customRoomType ?? 'Standard Room',
                  ),
                  _buildInfoRow('Status', housing.room!.occupancyStatus),
                  _buildInfoRow(
                    'Rental Price',
                    '${CurrencyFormatter.format(context, housing.room!.rentalPrice)} / month',
                  ),
                  if (housing.room!.size != null)
                    _buildInfoRow('Size', '${housing.room!.size} sq ft'),
                  if (housing.room!.floor != null)
                    _buildInfoRow('Floor', housing.room!.floor.toString()),
                  _buildInfoRow(
                    'Furnished',
                    housing.room!.isFurnished ? 'Yes' : 'No',
                  ),
                  if (housing.room!.description != null)
                    _buildInfoRow('Description', housing.room!.description!),
                ],
              ),
            ),

            // Room amenities
            if (housing.room!.amenities.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildInfoCard(
                title: 'Room Amenities',
                child: Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: housing.room!.amenities.map((amenity) {
                    return Chip(
                      label: Text(amenity.name),
                      backgroundColor: amenity.isCustom
                          ? Theme.of(context).colorScheme.secondaryContainer
                          : Theme.of(context).colorScheme.primaryContainer,
                    );
                  }).toList(),
                ),
              ),
            ],
          ],

          // Lease information
          const SizedBox(height: 24),
          Text(
            'Lease Information',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          _buildInfoCard(
            title: 'Lease Details',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('Status', housing.status),
                if (housing.leaseStartDate != null)
                  _buildInfoRow(
                    'Lease Start',
                    '${housing.leaseStartDate!.year}-${housing.leaseStartDate!.month.toString().padLeft(2, '0')}-${housing.leaseStartDate!.day.toString().padLeft(2, '0')}',
                  ),
                if (housing.leaseEndDate != null)
                  _buildInfoRow(
                    'Lease End',
                    '${housing.leaseEndDate!.year}-${housing.leaseEndDate!.month.toString().padLeft(2, '0')}-${housing.leaseEndDate!.day.toString().padLeft(2, '0')}',
                  ),
                if (housing.daysUntilLeaseEnd != null)
                  _buildInfoRow(
                    'Days Remaining',
                    housing.daysUntilLeaseEnd! > 0
                        ? '${housing.daysUntilLeaseEnd} days'
                        : 'Lease expired',
                    valueColor: housing.daysUntilLeaseEnd! > 30
                        ? Colors.green
                        : housing.daysUntilLeaseEnd! > 0
                        ? Colors.orange
                        : Colors.red,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({required String title, required Widget child}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const Divider(),
            const SizedBox(height: 8),
            child,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: valueColor),
            ),
          ),
        ],
      ),
    );
  }
}
